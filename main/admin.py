from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db.models import Count, Sum
from django.contrib.admin import SimpleListFilter
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import Group
from .models import (
    CustomUser, Admin, Accountant, Staff, Division, Department,
    Designation, Grade, Fixed, Deductions, ContractPay, RegularPay,
    Attendance, Payslip
)

# Unregister the default Group admin
admin.site.unregister(Group)

# Django Admin Site Configuration
admin.site.site_header = "Payroll Management System"
admin.site.site_title = "PMS Admin"
admin.site.index_title = "Welcome to PMS Administration"

# Custom Filters
class UserTypeFilter(SimpleListFilter):
    title = _('User Type')
    parameter_name = 'user_type'

    def lookups(self, request, model_admin):
        return (
            ('1', _('Admin')),
            ('2', _('Accountant')),
            ('3', _('Staff')),
        )

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(user_type=self.value())
        return queryset

class EmploymentTypeFilter(SimpleListFilter):
    title = _('Employment Type')
    parameter_name = 'employment_type'

    def lookups(self, request, model_admin):
        return (
            ('Regular', _('Regular')),
            ('Contract', _('Contract')),
            ('Active', _('Active')),
        )

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(employment_type=self.value())
        return queryset

# Inline Admin Classes
class AdminInline(admin.StackedInline):
    model = Admin
    can_delete = False
    verbose_name_plural = 'Admin Profile'
    extra = 0

class AccountantInline(admin.StackedInline):
    model = Accountant
    can_delete = False
    verbose_name_plural = 'Accountant Profile'
    extra = 0

class StaffInline(admin.StackedInline):
    model = Staff
    can_delete = False
    verbose_name_plural = 'Staff Profile'
    extra = 0
    fields = ('division', 'department', 'designation', 'emp_code', 'uan', 'emp_doj', 'grade', 'basic_amt', 'employment_type', 'is_active', 'cca')

# Custom User Admin
@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    list_display = ('email', 'first_name', 'last_name', 'user_type_display', 'gender', 'is_active', 'date_joined', 'profile_picture')
    list_filter = (UserTypeFilter, 'gender', 'is_active', 'date_joined')
    search_fields = ('email', 'first_name', 'last_name', 'father_name')
    ordering = ('email',)
    filter_horizontal = ('groups', 'user_permissions')

    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        (_('Personal info'), {'fields': ('first_name', 'last_name', 'father_name', 'gender', 'profile_pic')}),
        (_('Permissions'), {'fields': ('user_type', 'is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        (_('Important dates'), {'fields': ('last_login', 'date_joined')}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'password1', 'password2', 'user_type'),
        }),
    )

    def user_type_display(self, obj):
        colors = {1: 'red', 2: 'blue', 3: 'green'}
        types = {1: 'Admin', 2: 'Accountant', 3: 'Staff'}
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.user_type, 'black'),
            types.get(obj.user_type, 'Unknown')
        )
    user_type_display.short_description = 'User Type'

    def profile_picture(self, obj):
        if obj.profile_pic:
            return format_html('<img src="{}" width="30" height="30" style="border-radius: 50%;" />', obj.profile_pic.url)
        return "No Image"
    profile_picture.short_description = 'Profile Picture'

    def get_inline_instances(self, request, obj=None):
        if not obj:
            return []

        inlines = []
        if obj.user_type == 1:  # Admin
            inlines.append(AdminInline(self.model, self.admin_site))
        elif obj.user_type == 2:  # Accountant
            inlines.append(AccountantInline(self.model, self.admin_site))
        elif obj.user_type == 3:  # Staff
            inlines.append(StaffInline(self.model, self.admin_site))

        return [inline for inline in inlines]

# Admin Model Admin
@admin.register(Admin)
class AdminAdmin(admin.ModelAdmin):
    list_display = ('user_email', 'user_name', 'created_at')
    search_fields = ('user__email', 'user__first_name', 'user__last_name')
    list_filter = ('user__date_joined',)

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = 'Email'

    def user_name(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name}"
    user_name.short_description = 'Full Name'

    def created_at(self, obj):
        return obj.user.date_joined
    created_at.short_description = 'Created At'

# Accountant Model Admin
@admin.register(Accountant)
class AccountantAdmin(admin.ModelAdmin):
    list_display = ('user_email', 'user_name', 'created_at')
    search_fields = ('user__email', 'user__first_name', 'user__last_name')
    list_filter = ('user__date_joined',)

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = 'Email'

    def user_name(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name}"
    user_name.short_description = 'Full Name'

    def created_at(self, obj):
        return obj.user.date_joined
    created_at.short_description = 'Created At'

# Staff Model Admin
@admin.register(Staff)
class StaffAdmin(admin.ModelAdmin):
    list_display = ('emp_code', 'user_name', 'user_email', 'department', 'designation', 'employment_type', 'is_active', 'basic_amt')
    list_filter = (EmploymentTypeFilter, 'is_active', 'division', 'department', 'designation', 'grade')
    search_fields = ('emp_code', 'user__email', 'user__first_name', 'user__last_name', 'uan')
    list_editable = ('is_active',)
    list_per_page = 25

    fieldsets = (
        (_('User Information'), {
            'fields': ('user',)
        }),
        (_('Employment Details'), {
            'fields': ('emp_code', 'uan', 'emp_doj', 'employment_type', 'is_active')
        }),
        (_('Organization Structure'), {
            'fields': ('division', 'department', 'designation', 'grade')
        }),
        (_('Salary Information'), {
            'fields': ('basic_amt', 'cca')
        }),
    )

    def user_name(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name}"
    user_name.short_description = 'Full Name'

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = 'Email'

# Division Model Admin
@admin.register(Division)
class DivisionAdmin(admin.ModelAdmin):
    list_display = ('name', 'department_count', 'staff_count', 'created_at')
    search_fields = ('name',)
    list_filter = ('created_at',)

    def department_count(self, obj):
        return obj.department_set.count()
    department_count.short_description = 'Departments'

    def staff_count(self, obj):
        return obj.staff_set.count()
    staff_count.short_description = 'Staff Members'

# Department Model Admin
@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'division', 'designation_count', 'staff_count', 'created_at')
    list_filter = ('division', 'created_at')
    search_fields = ('code', 'name', 'division__name')

    def designation_count(self, obj):
        return obj.designation_set.count()
    designation_count.short_description = 'Designations'

    def staff_count(self, obj):
        return obj.staff_set.count()
    staff_count.short_description = 'Staff Members'

# Designation Model Admin
@admin.register(Designation)
class DesignationAdmin(admin.ModelAdmin):
    list_display = ('name', 'department', 'division_name', 'staff_count', 'created_at')
    list_filter = ('department__division', 'department', 'created_at')
    search_fields = ('name', 'department__name', 'department__division__name')

    def division_name(self, obj):
        return obj.department.division.name
    division_name.short_description = 'Division'

    def staff_count(self, obj):
        return obj.staff_set.count()
    staff_count.short_description = 'Staff Members'

# Grade Model Admin
@admin.register(Grade)
class GradeAdmin(admin.ModelAdmin):
    list_display = ('name', 'salary_range', 'increment', 'allowances_total', 'staff_count', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('name',)

    def salary_range(self, obj):
        return f"₹{obj.start:,} - ₹{obj.end:,}"
    salary_range.short_description = 'Salary Range'

    def allowances_total(self, obj):
        total = obj.medical + obj.adhoc + obj.conva + obj.cca
        return f"₹{total:,}"
    allowances_total.short_description = 'Total Allowances'

    def staff_count(self, obj):
        return obj.staff_set.count()
    staff_count.short_description = 'Staff Members'

# Fixed Model Admin
@admin.register(Fixed)
class FixedAdmin(admin.ModelAdmin):
    list_display = ('division', 'month', 'da_percentage', 'hra_percentage')
    list_filter = ('division', 'month')
    search_fields = ('division__name',)
    date_hierarchy = 'month'

    def da_percentage(self, obj):
        return f"{obj.da}%" if obj.da else "0%"
    da_percentage.short_description = 'DA %'

    def hra_percentage(self, obj):
        return f"{obj.hra}%" if obj.hra else "0%"
    hra_percentage.short_description = 'HRA %'

# Deductions Model Admin
@admin.register(Deductions)
class DeductionsAdmin(admin.ModelAdmin):
    list_display = ('staff_name', 'staff_emp_code', 'total_deductions', 'income_tax', 'canteen', 'advance', 'society', 'insurance', 'other')
    list_filter = ('staff__division', 'staff__department', 'staff__employment_type')
    search_fields = ('staff__emp_code', 'staff__user__first_name', 'staff__user__last_name', 'staff__user__email')

    def staff_name(self, obj):
        return f"{obj.staff.user.first_name} {obj.staff.user.last_name}"
    staff_name.short_description = 'Staff Name'

    def staff_emp_code(self, obj):
        return obj.staff.emp_code
    staff_emp_code.short_description = 'Employee Code'

    def total_deductions(self, obj):
        total = (obj.income_tax or 0) + (obj.canteen or 0) + (obj.advance or 0) + (obj.society or 0) + (obj.insurance or 0) + (obj.other or 0)
        return f"₹{total:,}"
    total_deductions.short_description = 'Total Deductions'

# ContractPay Model Admin
@admin.register(ContractPay)
class ContractPayAdmin(admin.ModelAdmin):
    list_display = ('staff_name', 'staff_emp_code', 'month', 'total_pay', 'adhoc', 'hra', 'arrears', 'other')
    list_filter = ('month', 'staff__division', 'staff__department')
    search_fields = ('staff__emp_code', 'staff__user__first_name', 'staff__user__last_name')
    date_hierarchy = 'month'

    def staff_name(self, obj):
        return f"{obj.staff.user.first_name} {obj.staff.user.last_name}"
    staff_name.short_description = 'Staff Name'

    def staff_emp_code(self, obj):
        return obj.staff.emp_code
    staff_emp_code.short_description = 'Employee Code'

    def total_pay(self, obj):
        total = (obj.adhoc or 0) + (obj.hra or 0) + (obj.arrears or 0) + (obj.other or 0)
        return f"₹{total:,}"
    total_pay.short_description = 'Total Pay'

# RegularPay Model Admin
@admin.register(RegularPay)
class RegularPayAdmin(admin.ModelAdmin):
    list_display = ('staff_name', 'staff_emp_code', 'total_pay', 'arrears', 'other')
    list_filter = ('staff__division', 'staff__department', 'staff__grade')
    search_fields = ('staff__emp_code', 'staff__user__first_name', 'staff__user__last_name')

    def staff_name(self, obj):
        return f"{obj.staff.user.first_name} {obj.staff.user.last_name}"
    staff_name.short_description = 'Staff Name'

    def staff_emp_code(self, obj):
        return obj.staff.emp_code
    staff_emp_code.short_description = 'Employee Code'

    def total_pay(self, obj):
        total = (obj.arrears or 0) + (obj.other or 0)
        return f"₹{total:,}"
    total_pay.short_description = 'Total Pay'

# Attendance Model Admin
@admin.register(Attendance)
class AttendanceAdmin(admin.ModelAdmin):
    list_display = ('staff_name', 'staff_emp_code', 'paid_days', 'lop', 'effective_days')
    list_filter = ('staff__division', 'staff__department', 'staff__employment_type')
    search_fields = ('staff__emp_code', 'staff__user__first_name', 'staff__user__last_name')

    def staff_name(self, obj):
        return f"{obj.staff.user.first_name} {obj.staff.user.last_name}"
    staff_name.short_description = 'Staff Name'

    def staff_emp_code(self, obj):
        return obj.staff.emp_code
    staff_emp_code.short_description = 'Employee Code'

    def effective_days(self, obj):
        if obj.paid_days and obj.lop:
            return obj.paid_days - obj.lop
        return obj.paid_days or 0
    effective_days.short_description = 'Effective Days'

# Payslip Model Admin
@admin.register(Payslip)
class PayslipAdmin(admin.ModelAdmin):
    list_display = ('staff_name', 'staff_emp_code', 'month_year', 'gross_pay_display', 'total_deductions_display', 'net_pay_display', 'view_payslip_link')
    list_filter = ('month', 'staff__division', 'staff__department', 'staff__employment_type')
    search_fields = ('staff__emp_code', 'staff__user__first_name', 'staff__user__last_name')
    date_hierarchy = 'month'
    readonly_fields = ('created_at',)
    list_per_page = 25

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('staff', 'month')
        }),
        (_('Earnings'), {
            'fields': ('basic', 'da', 'hra', 'conv', 'medical', 'cca', 'adhoc', 'Pother', 'arrears', 'gross_pay'),
            'classes': ('collapse',)
        }),
        (_('Deductions'), {
            'fields': ('epf', 'esi', 'income_tax', 'canteen', 'advance', 'society', 'insurance', 'Dother', 'total_deductions'),
            'classes': ('collapse',)
        }),
        (_('Attendance'), {
            'fields': ('paid_days', 'lop'),
            'classes': ('collapse',)
        }),
        (_('Final Calculation'), {
            'fields': ('net_pay', 'created_at')
        }),
    )

    def staff_name(self, obj):
        return f"{obj.staff.user.first_name} {obj.staff.user.last_name}"
    staff_name.short_description = 'Staff Name'

    def staff_emp_code(self, obj):
        return obj.staff.emp_code
    staff_emp_code.short_description = 'Employee Code'

    def month_year(self, obj):
        return obj.month.strftime('%B %Y')
    month_year.short_description = 'Month/Year'

    def gross_pay_display(self, obj):
        return f"₹{obj.gross_pay:,}"
    gross_pay_display.short_description = 'Gross Pay'

    def total_deductions_display(self, obj):
        return f"₹{obj.total_deductions:,}"
    total_deductions_display.short_description = 'Total Deductions'

    def net_pay_display(self, obj):
        return format_html('<strong style="color: green;">₹{:,}</strong>', obj.net_pay)
    net_pay_display.short_description = 'Net Pay'

    def view_payslip_link(self, obj):
        url = reverse('view_payslip', args=[obj.staff.id, obj.month.strftime('%Y-%m-%d')])
        return format_html('<a href="{}" target="_blank" class="btn btn-sm btn-info">View Payslip</a>', url)
    view_payslip_link.short_description = 'Actions'

    actions = ['generate_payslip_report']

    def generate_payslip_report(self, request, queryset):
        # Custom action to generate payslip reports
        selected = queryset.count()
        self.message_user(request, f'Generated report for {selected} payslips.')
    generate_payslip_report.short_description = "Generate payslip report for selected items"

# Custom Admin Actions
def make_staff_active(modeladmin, request, queryset):
    queryset.update(is_active=True)
    modeladmin.message_user(request, f"{queryset.count()} staff members marked as active.")
make_staff_active.short_description = "Mark selected staff as active"

def make_staff_inactive(modeladmin, request, queryset):
    queryset.update(is_active=False)
    modeladmin.message_user(request, f"{queryset.count()} staff members marked as inactive.")
make_staff_inactive.short_description = "Mark selected staff as inactive"

# Add actions to StaffAdmin
StaffAdmin.actions = [make_staff_active, make_staff_inactive]